#!/usr/bin/env python3
"""
Simple Proposal Generator Script

This script generates a proposal using the RFP Generation Service.
It mimics exactly what the endpoint does.

Usage:
    python generate_proposal.py
"""

import asyncio
import json
import sys
import os

# Add the AIService directory to the Python path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'AIService'))

from services.proposal.rfp.generate_rfp import RFPGenerationService


async def main():
    """Generate a proposal using the RFP Generation Service"""
    
    # Configuration - Update these values as needed
    opportunity_id = "rwpWgMHaAC"  # Change this to your opportunity ID
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"  # Change this to your tenant ID
    client_short_name = "adeptengineeringsolutions"  # Change this to your client
    source = "custom"  # Options: "sam", "ebuy", "custom"
    
    print("🚀 PROPOSAL GENERATOR")
    print("=" * 50)
    print(f"Opportunity ID: {opportunity_id}")
    print(f"Tenant ID: {tenant_id}")
    print(f"Client: {client_short_name}")
    print(f"Source: {source}")
    print("=" * 50)

    # Create job instruction (same format as the API endpoint)
    job_instruction = {
        "opportunityId": opportunity_id,
        "clientShortName": client_short_name,
        "tenantId": tenant_id,
        "profileId": "2",
        "opportunityType": source,
        "sourceDocuments": [],
        "forceRefresh": False,
        "setForReview": True,  # Set to False if you want it in format queue instead
        "exportType": 1,
        "proposalRequestType": 1,
        "coverPage": None,
        "trailingPage": None,
        "systemPromptParameters": None,
        "isRFP": True,
        "aiPersonalityId": "6"
    }

    print("📋 Job Configuration:")
    print(json.dumps(job_instruction, indent=2))
    print("=" * 50)

    try:
        print("🔧 Initializing RFP Generation Service...")
        rfp_generator = RFPGenerationService()
        
        print("🚀 Starting proposal generation...")
        await rfp_generator.generate_rfp(
            job_instruction=json.dumps(job_instruction), 
            job_submitted_by="script_user"
        )
        
        print("✅ Proposal generation completed successfully!")
        print()
        print("💡 Next steps:")
        if job_instruction["setForReview"]:
            print("   - Check the 'proposals_in_review' table in your database")
            print("   - The proposal is ready for review")
        else:
            print("   - Check the 'proposals_format_queue' table in your database")
            print("   - The proposal is ready for formatting/export")
        
    except Exception as e:
        print(f"❌ Error during proposal generation: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
