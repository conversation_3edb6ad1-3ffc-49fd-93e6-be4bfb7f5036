#!/usr/bin/env python3
"""
Simple Proposal Generator Script

This script generates a proposal using the exact same approach as your pipeline.py file.
It uses the opportunity ID you have selected: "rwpWgMHaAC"

Usage:
    python scripts/simple_proposal_generator.py
"""

import asyncio
import json
import sys
import os

# Add the parent directory to the Python path so we can import from services
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.proposal.rfp.generate_rfp import RFPGenerationService


async def main():
    """Generate a proposal using the RFP Generation Service directly"""
    
    # Use the opportunity ID from your selected code
    opportunity_id = "rwpWgMHaAC"
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    source = "custom"

    print("="*80)
    print("🚀 SIMPLE PROPOSAL GENERATOR")
    print("="*80)
    print(f"Opportunity ID: {opportunity_id}")
    print(f"Tenant ID: {tenant_id}")
    print(f"Source: {source}")
    print("="*80)

    # Create job instruction (same format as pipeline_5.py)
    job_instruction = {
        "opportunityId": opportunity_id,
        "clientShortName": "adeptengineeringsolutions",
        "tenantId": tenant_id,
        "profileId": "2",
        "opportunityType": source,
        "sourceDocuments": [],
        "forceRefresh": False,
        "setForReview": True,
        "exportType": 1,
        "proposalRequestType": 1,
        "coverPage": None,
        "trailingPage": None,
        "systemPromptParameters": None,
        "isRFP": True,
        # "generatedVolumes": [1],  # Generate only Volume 1 for faster testing
        "aiPersonalityId": "6"
    }

    print("📋 Job Instruction:")
    print(json.dumps(job_instruction, indent=2))
    print("="*80)

    try:
        print("🔧 Initializing RFP Generation Service...")
        rfp_generator = RFPGenerationService()
        
        print("🚀 Starting proposal generation...")
        await rfp_generator.generate_rfp(
            job_instruction=json.dumps(job_instruction), 
            job_submitted_by="69"
        )
        
        print("✅ Proposal generation completed successfully!")
        print("💡 Check the database for the generated proposal data.")
        print("💡 If setForReview=True, the proposal will be in the proposals_in_review table.")
        print("💡 If setForReview=False, the proposal will be in the proposals_format_queue table.")
        
    except Exception as e:
        print(f"❌ Error during proposal generation: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
